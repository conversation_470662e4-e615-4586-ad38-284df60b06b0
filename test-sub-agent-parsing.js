/**
 * Test script to verify sub-agent <PERSON><PERSON><PERSON> parsing and validation
 */

const { z } = require('zod');

// Zod schemas (copied from the implementation)
const SubAgentPrioritySchema = z.enum(['high', 'medium', 'low']);
const SubAgentComplexitySchema = z.enum(['simple', 'moderate', 'complex']);

const SubAgentRawDataSchema = z.object({
  specialization: z.string().min(1, 'Specialization is required'),
  assignment: z.string().min(10, 'Assignment must be at least 10 characters'),
  priority: SubAgentPrioritySchema.optional(),
  complexity: SubAgentComplexitySchema.optional(),
  estimatedComplexity: SubAgentComplexitySchema.optional(),
  agentName: z.string().optional(),
  tools: z.array(z.string()).optional(),
  rationale: z.string().optional()
});

const SubAgentArraySchema = z.array(SubAgentRawDataSchema).min(1, 'At least one sub-agent is required');

/**
 * Extract JSON from a response that may be wrapped in markdown code blocks
 */
function extractJsonFromResponse(response) {
  const trimmed = response.trim();
  
  const jsonBlockRegex = /```(?:json)?\s*([\s\S]*?)\s*```/i;
  const match = trimmed.match(jsonBlockRegex);
  
  if (match) {
    return match[1].trim();
  }
  
  return trimmed;
}

/**
 * Parse and validate sub-agent data using Zod schema
 */
function parseAndValidateSubAgents(response) {
  const jsonString = extractJsonFromResponse(response);
  
  let rawData;
  try {
    rawData = JSON.parse(jsonString);
  } catch (parseError) {
    throw new Error(`Failed to parse JSON: ${parseError.message}`);
  }
  
  const validationResult = SubAgentArraySchema.safeParse(rawData);
  
  if (!validationResult.success) {
    const errorMessages = validationResult.error.errors.map(err => 
      `${err.path.join('.')}: ${err.message}`
    ).join('; ');
    throw new Error(`Sub-agent validation failed: ${errorMessages}`);
  }
  
  return validationResult.data;
}

// Test cases
const testCases = [
  {
    name: "Valid JSON wrapped in markdown",
    input: `\`\`\`json
[
  {
    "specialization": "User Guide Creation",
    "assignment": "Create comprehensive user journey documentation for each of the three core personas",
    "priority": "high",
    "complexity": "complex"
  },
  {
    "specialization": "Technical Analysis", 
    "assignment": "Analyze the AI-powered feedback system architecture and speech recognition capabilities",
    "priority": "high",
    "complexity": "complex"
  }
]
\`\`\``,
    shouldPass: true
  },
  {
    name: "Valid JSON without markdown",
    input: `[
  {
    "specialization": "API Documentation",
    "assignment": "Document all REST API endpoints with examples and response formats",
    "priority": "medium"
  }
]`,
    shouldPass: true
  },
  {
    name: "Invalid JSON - missing required field",
    input: `\`\`\`json
[
  {
    "specialization": "Technical Analysis"
  }
]
\`\`\``,
    shouldPass: false
  },
  {
    name: "Invalid JSON - assignment too short",
    input: `[
  {
    "specialization": "Technical Analysis",
    "assignment": "Short"
  }
]`,
    shouldPass: false
  }
];

// Run tests
console.log('Testing sub-agent JSON parsing and validation...\n');

testCases.forEach((testCase, index) => {
  console.log(`Test ${index + 1}: ${testCase.name}`);
  
  try {
    const result = parseAndValidateSubAgents(testCase.input);
    
    if (testCase.shouldPass) {
      console.log('✅ PASS - Successfully parsed and validated');
      console.log(`   Found ${result.length} sub-agent(s)`);
      result.forEach((agent, i) => {
        console.log(`   Agent ${i + 1}: ${agent.specialization} (${agent.priority || 'medium'} priority)`);
      });
    } else {
      console.log('❌ FAIL - Expected validation to fail but it passed');
    }
  } catch (error) {
    if (!testCase.shouldPass) {
      console.log('✅ PASS - Correctly failed validation');
      console.log(`   Error: ${error.message}`);
    } else {
      console.log('❌ FAIL - Expected validation to pass but it failed');
      console.log(`   Error: ${error.message}`);
    }
  }
  
  console.log('');
});

console.log('Test completed!');
