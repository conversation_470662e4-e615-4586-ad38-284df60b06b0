/**
 * Adaptable Tool System for Multiple LLM Providers
 * 
 * This system provides a unified interface for tool calling across different LLM providers
 * (Anthropic, OpenAI, Google, etc.) while handling provider-specific tool formats.
 */

export type LLMProvider = 'anthropic' | 'openai' | 'google' | 'groq' | 'deepseek';

// Base tool definition interface
export interface BaseToolDefinition {
  name: string;
  description: string;
  parameters: {
    type: 'object';
    properties: Record<string, any>;
    required?: string[];
  };
}

// Provider-specific tool formats
export interface AnthropicToolDefinition {
  type: 'custom';
  name: string;
  description: string;
  input_schema: {
    type: 'object';
    properties: Record<string, any>;
    required?: string[];
  };
}

export interface OpenAIToolDefinition {
  type: 'function';
  function: {
    name: string;
    description: string;
    parameters: {
      type: 'object';
      properties: Record<string, any>;
      required?: string[];
    };
  };
}

export interface GoogleToolDefinition {
  name: string;
  description: string;
  parameters: {
    type: 'object';
    properties: Record<string, any>;
    required?: string[];
  };
}

// Tool call interfaces
export interface BaseToolCall {
  name: string;
  input: Record<string, any>;
}

export interface AnthropicToolCall extends BaseToolCall {
  type: 'tool_use';
  id: string;
}

export interface OpenAIToolCall extends BaseToolCall {
  id: string;
  type: 'function';
  function: {
    name: string;
    arguments: string; // JSON string
  };
}

// Tool execution result
export interface ToolExecutionResult {
  success: boolean;
  result?: any;
  error?: string;
  artifacts?: {
    charts?: any[];
    dashboards?: any[];
  };
}

// Tool system configuration
export interface ToolSystemConfig {
  provider: LLMProvider;
  enabledTools: {
    chartGeneration?: boolean;
    dashboardCreation?: boolean;
    webSearch?: boolean;
    datetime?: boolean;
    questionAnswer?: boolean;
  };
  toolComplexity?: 'simple' | 'moderate' | 'complex';
}

/**
 * Abstract base class for tool system adapters
 */
export abstract class ToolSystemAdapter {
  protected config: ToolSystemConfig;

  constructor(config: ToolSystemConfig) {
    this.config = config;
  }

  /**
   * Convert base tool definitions to provider-specific format
   */
  abstract formatToolsForProvider(tools: BaseToolDefinition[]): any[];

  /**
   * Parse tool calls from provider response
   */
  abstract parseToolCalls(response: any): BaseToolCall[];

  /**
   * Execute a tool call
   */
  abstract executeToolCall(toolCall: BaseToolCall): Promise<ToolExecutionResult>;

  /**
   * Get available tools based on configuration
   */
  getAvailableTools(): BaseToolDefinition[] {
    const tools: BaseToolDefinition[] = [];

    if (this.config.enabledTools.chartGeneration) {
      tools.push({
        name: 'generate_chart',
        description: 'Generate a chart visualization to enhance documentation understanding',
        parameters: {
          type: 'object',
          properties: {
            prompt: {
              type: 'string',
              description: 'Detailed description of the chart to generate'
            },
            chartType: {
              type: 'string',
              enum: ['bar', 'line', 'pie', 'scatter', 'flow', 'heatmap', 'table', 'bubble'],
              description: 'Type of chart most appropriate for the data'
            }
          },
          required: ['prompt', 'chartType']
        }
      });
    }

    if (this.config.enabledTools.dashboardCreation) {
      tools.push({
        name: 'create_dashboard',
        description: 'Create a multi-chart dashboard for comprehensive visualization',
        parameters: {
          type: 'object',
          properties: {
            title: { type: 'string', description: 'Dashboard title' },
            description: { type: 'string', description: 'Dashboard description' },
            charts: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  prompt: { type: 'string' },
                  chartType: { type: 'string' }
                }
              }
            }
          },
          required: ['title', 'description', 'charts']
        }
      });
    }

    if (this.config.enabledTools.webSearch) {
      tools.push({
        name: 'web_search',
        description: 'Search the internet for current information and best practices',
        parameters: {
          type: 'object',
          properties: {
            query: { type: 'string', description: 'Search query' },
            numResults: { type: 'number', description: 'Number of results (default: 5)' }
          },
          required: ['query']
        }
      });
    }

    if (this.config.enabledTools.datetime) {
      tools.push({
        name: 'get_datetime',
        description: 'Get current date and time for documentation timestamps',
        parameters: {
          type: 'object',
          properties: {
            format: { type: 'string', enum: ['full', 'long', 'medium', 'short'] },
            includeTime: { type: 'boolean' }
          }
        }
      });
    }

    if (this.config.enabledTools.questionAnswer) {
      tools.push({
        name: 'ask_question',
        description: 'Ask questions to gather additional context or clarify requirements',
        parameters: {
          type: 'object',
          properties: {
            query: { type: 'string', description: 'Question to ask' },
            context: { type: 'string', description: 'Additional context' }
          },
          required: ['query']
        }
      });
    }

    return tools;
  }

  /**
   * Check if any tools are enabled
   */
  hasEnabledTools(): boolean {
    const { enabledTools } = this.config;
    return !!(
      enabledTools.chartGeneration ||
      enabledTools.dashboardCreation ||
      enabledTools.webSearch ||
      enabledTools.datetime ||
      enabledTools.questionAnswer
    );
  }
}

// Factory function moved to ToolSystemFactory.ts to avoid circular imports
