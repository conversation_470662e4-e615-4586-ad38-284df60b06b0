/**
 * Enhanced LLM Processor with Tool Support
 * 
 * This processor provides a unified interface for calling different LLM providers
 * with tool support, handling provider-specific formats and tool execution.
 */

import { processWithAnthropic } from '../../../tools/anthropic-ai';
// Note: Other LLM processors may need to be implemented or imported differently
// import { processWithOpenAI } from '../../../tools/openai-ai';
// import { processWithGoogleAI } from '../../../tools/google-ai';
// import { processWithGroq } from '../../../tools/groq-ai';
// import { processWithDeepSeek } from '../../../tools/deepseek-ai';

import {
  LLMProvider,
  ToolSystemConfig,
  ToolSystemAdapter,
  createToolSystemAdapter,
  BaseToolCall,
  ToolExecutionResult
} from './ToolSystemAdapter';

export interface LLMProcessingOptions {
  prompt: string;
  model?: string;
  maxTokens?: number;
  temperature?: number;
  provider: LLMProvider;
  toolConfig?: ToolSystemConfig;
}

export interface LLMProcessingResult {
  content: string;
  toolCalls?: BaseToolCall[];
  toolResults?: ToolExecutionResult[];
  artifacts?: {
    charts?: any[];
    dashboards?: any[];
  };
  provider: LLMProvider;
  model?: string;
  executionTimeMs: number;
}

export class EnhancedLLMProcessor {
  private toolAdapter?: ToolSystemAdapter;

  constructor(toolConfig?: ToolSystemConfig) {
    if (toolConfig) {
      this.toolAdapter = createToolSystemAdapter(toolConfig);
    }
  }

  /**
   * Process a prompt with the specified LLM provider, optionally with tool support
   */
  async processWithProvider(options: LLMProcessingOptions): Promise<LLMProcessingResult> {
    const startTime = Date.now();
    
    try {
      // Prepare tools if tool adapter is available and has enabled tools
      let formattedTools: any[] = [];
      if (this.toolAdapter && this.toolAdapter.hasEnabledTools()) {
        const availableTools = this.toolAdapter.getAvailableTools();
        formattedTools = this.toolAdapter.formatToolsForProvider(availableTools);
      }

      // Call the appropriate LLM provider
      let response: any;
      let content: string;

      switch (options.provider) {
        case 'anthropic':
          response = await this.processWithAnthropic(options, formattedTools);
          content = typeof response === 'string' ? response : String(response);
          break;

        case 'openai':
          // TODO: Implement OpenAI processing
          throw new Error('OpenAI processing not yet implemented');

        case 'google':
          // TODO: Implement Google AI processing
          throw new Error('Google AI processing not yet implemented');

        case 'groq':
          // TODO: Implement Groq processing
          throw new Error('Groq processing not yet implemented');

        case 'deepseek':
          // TODO: Implement DeepSeek processing
          throw new Error('DeepSeek processing not yet implemented');

        default:
          throw new Error(`Unsupported LLM provider: ${options.provider}`);
      }

      // Parse tool calls if tool adapter is available
      let toolCalls: BaseToolCall[] = [];
      let toolResults: ToolExecutionResult[] = [];
      let artifacts: any = {};

      if (this.toolAdapter) {
        toolCalls = this.toolAdapter.parseToolCalls(response);
        
        // Execute tool calls
        if (toolCalls.length > 0) {
          toolResults = await this.executeToolCalls(toolCalls);
          
          // Aggregate artifacts
          artifacts = this.aggregateArtifacts(toolResults);
          
          // Append tool results to content
          const toolSummary = this.generateToolSummary(toolResults);
          if (toolSummary) {
            content += '\n\n' + toolSummary;
          }
        }
      }

      const executionTimeMs = Date.now() - startTime;

      return {
        content,
        toolCalls: toolCalls.length > 0 ? toolCalls : undefined,
        toolResults: toolResults.length > 0 ? toolResults : undefined,
        artifacts: Object.keys(artifacts).length > 0 ? artifacts : undefined,
        provider: options.provider,
        model: options.model,
        executionTimeMs
      };

    } catch (error: any) {
      const executionTimeMs = Date.now() - startTime;
      
      return {
        content: `Error processing with ${options.provider}: ${error.message}`,
        provider: options.provider,
        model: options.model,
        executionTimeMs
      };
    }
  }

  /**
   * Process with Anthropic (currently without tool support)
   */
  private async processWithAnthropic(options: LLMProcessingOptions, tools: any[]): Promise<any> {
    // Note: Current processWithAnthropic doesn't support tools
    // Tools are ignored for now until proper support is implemented
    return await processWithAnthropic({
      prompt: options.prompt,
      model: options.model || 'claude-sonnet-4-20250514',
      max_tokens: options.maxTokens || 4000,
      temperature: options.temperature || 0.3
    });
  }

  /**
   * Process with OpenAI (with tool support)
   * TODO: Implement when OpenAI integration is available
   */
  private async processWithOpenAI(_options: LLMProcessingOptions, _tools: any[]): Promise<any> {
    throw new Error('OpenAI processing not yet implemented');
  }

  /**
   * Process with Google AI
   * TODO: Implement when Google AI integration is available
   */
  private async processWithGoogle(_options: LLMProcessingOptions, _tools: any[]): Promise<any> {
    throw new Error('Google AI processing not yet implemented');
  }

  /**
   * Process with Groq
   * TODO: Implement when Groq integration is available
   */
  private async processWithGroq(_options: LLMProcessingOptions, _tools: any[]): Promise<any> {
    throw new Error('Groq processing not yet implemented');
  }

  /**
   * Process with DeepSeek
   * TODO: Implement when DeepSeek integration is available
   */
  private async processWithDeepSeek(_options: LLMProcessingOptions, _tools: any[]): Promise<any> {
    throw new Error('DeepSeek processing not yet implemented');
  }

  /**
   * Execute tool calls
   */
  private async executeToolCalls(toolCalls: BaseToolCall[]): Promise<ToolExecutionResult[]> {
    if (!this.toolAdapter) {
      return [];
    }

    const results: ToolExecutionResult[] = [];
    
    for (const toolCall of toolCalls) {
      try {
        const result = await this.toolAdapter.executeToolCall(toolCall);
        results.push(result);
      } catch (error: any) {
        results.push({
          success: false,
          error: `Failed to execute tool ${toolCall.name}: ${error.message}`
        });
      }
    }

    return results;
  }

  /**
   * Aggregate artifacts from tool results
   */
  private aggregateArtifacts(toolResults: ToolExecutionResult[]): any {
    const artifacts: any = {
      charts: [],
      dashboards: []
    };

    for (const result of toolResults) {
      if (result.success && result.artifacts) {
        if (result.artifacts.charts) {
          artifacts.charts.push(...result.artifacts.charts);
        }
        if (result.artifacts.dashboards) {
          artifacts.dashboards.push(...result.artifacts.dashboards);
        }
      }
    }

    return artifacts;
  }

  /**
   * Generate a summary of tool execution results
   */
  private generateToolSummary(toolResults: ToolExecutionResult[]): string {
    const summaries: string[] = [];

    for (const result of toolResults) {
      if (result.success) {
        summaries.push(`✅ Tool executed successfully`);
      } else {
        summaries.push(`❌ Tool execution failed: ${result.error}`);
      }
    }

    return summaries.length > 0 ? `\n**Tool Execution Summary:**\n${summaries.join('\n')}` : '';
  }
}
