/**
 * Create Documentation Agent
 *
 * This agent wraps the createDocumentationTool to provide:
 * 1. Agent-specific logging and error handling
 * 2. Metadata tracking and execution metrics
 * 3. Agent-compatible result format
 * 4. Future extensibility for agent-based enhancements
 */

import { createDocumentationTool, CreateDocumentationToolOptions } from '../../tools/createDocumentationTool';
import { createPMORecordFromForm, updatePMORecord } from '../../firebase/pmoCollection';
import { AgenticTeamId } from '../pmo/PMOInterfaces';
import { adminDb } from '../../../components/firebase-admin';
import { processWithAnthropic } from '../../tools/anthropic-ai';
import { ChartTool } from '../../tools/chart-tool';
import { DashboardTool } from '../../tools/dashboard-tool';
import { internetSearchTool } from '../../tools/internetSearchTool';
import { dateTimeTool } from '../../tools/dateTimeTool';
import { questionAnswerTool } from '../../tools/questionAnswerTool';
import { queryDocumentsTool } from '../../tools/queryDocumentsTool';
import { z } from 'zod';

// Zod schemas for sub-agent validation
const SubAgentPrioritySchema = z.enum(['high', 'medium', 'low']);
const SubAgentComplexitySchema = z.enum(['simple', 'moderate', 'complex']);

const SubAgentRawDataSchema = z.object({
  specialization: z.string().min(1, 'Specialization is required'),
  assignment: z.string().min(10, 'Assignment must be at least 10 characters'),
  priority: SubAgentPrioritySchema.optional(),
  complexity: SubAgentComplexitySchema.optional(),
  estimatedComplexity: SubAgentComplexitySchema.optional(), // Alternative field name
  agentName: z.string().optional(),
  tools: z.array(z.string()).optional(),
  rationale: z.string().optional()
});

const SubAgentArraySchema = z.array(SubAgentRawDataSchema).min(1, 'At least one sub-agent is required');

/**
 * Extract JSON from a response that may be wrapped in markdown code blocks
 */
function extractJsonFromResponse(response: string): string {
  // Remove leading/trailing whitespace
  const trimmed = response.trim();

  // Check if response is wrapped in markdown code blocks
  const jsonBlockRegex = /```(?:json)?\s*([\s\S]*?)\s*```/i;
  const match = trimmed.match(jsonBlockRegex);

  if (match) {
    return match[1].trim();
  }

  // If no code block found, return the original response
  return trimmed;
}

/**
 * Parse and validate sub-agent data using Zod schema
 */
function parseAndValidateSubAgents(response: string): z.infer<typeof SubAgentArraySchema> {
  // Extract JSON from potential markdown wrapper
  const jsonString = extractJsonFromResponse(response);

  // Parse JSON
  let rawData: unknown;
  try {
    rawData = JSON.parse(jsonString);
  } catch (parseError) {
    throw new Error(`Failed to parse JSON: ${parseError instanceof Error ? parseError.message : 'Unknown parsing error'}`);
  }

  // Validate with Zod schema
  const validationResult = SubAgentArraySchema.safeParse(rawData);

  if (!validationResult.success) {
    const errorMessages = validationResult.error.errors.map(err =>
      `${err.path.join('.')}: ${err.message}`
    ).join('; ');
    throw new Error(`Sub-agent validation failed: ${errorMessages}`);
  }

  return validationResult.data;
}

export interface CreateDocumentationAgentOptions extends CreateDocumentationToolOptions {
  // Agent-specific options
  agentId?: string;
  includeExplanation?: boolean;
  streamResponse?: boolean;
  onStreamUpdate?: (update: CreateDocumentationStreamUpdate) => void;
  // PMO integration options
  createPMORecord?: boolean; // Whether to create a PMO record for tracking
  pmoRecordTitle?: string; // Custom title for the PMO record
  // Sub-agent options (MANDATORY)
  useSubAgents: boolean; // Whether to use sub-agents for enhanced documentation (REQUIRED)
  maxSubAgents?: number; // Maximum number of sub-agents to create
  subAgentComplexity?: 'simple' | 'moderate' | 'complex'; // Complexity level for sub-agent tool enablement
}

export interface CreateDocumentationStreamUpdate {
  stage: 'initializing' | 'querying-codebase' | 'creating-sub-agents' | 'executing-sub-agents' | 'consolidating-results' | 'generating-content' | 'creating-pdf' | 'complete';
  data?: any;
  message?: string;
  progress?: number; // 0-100
  subAgentProgress?: {
    completed: number;
    total: number;
    currentAgent?: string;
  };
}

// Sub-agent interfaces
export interface DocumentationSubAgent {
  agentId: string;
  agentName: string;
  specialization: string;
  assignment: string;
  priority: 'high' | 'medium' | 'low';
  estimatedComplexity: 'simple' | 'moderate' | 'complex';
  enabledTools?: {
    chartGeneration?: boolean;
    dashboardCreation?: boolean;
    webSearch?: boolean;
    datetime?: boolean;
    questionAnswer?: boolean;
  };
  toolUseGuidance?: string;
}

export interface DocumentationSubAgentResult {
  agentId: string;
  agentName: string;
  specialization: string;
  assignment: string;
  output: string;
  success: boolean;
  error?: string;
  artifacts?: {
    charts?: any[];
    dashboards?: any[];
  };
  executionTimeMs?: number;
}

export interface CreateDocumentationAgentResult {
  success: boolean;
  query: string;
  category: string;
  strategy: string;
  markdownContent: string;
  rawData: any; // StrategyRun | ABTestResult from the tool
  generatedTitle?: string;
  pdfResult?: {
    documentId: string;
    downloadUrl: string;
  };
  error?: string;
  metrics?: {
    queryDurationMs: number;
    totalMatches: number;
    namespacesQueried: string[];
  };
  // PMO integration results
  pmoRecord?: {
    pmoRecordId: string;
    assignedTeams: string[]; // AgenticTeamId values
  };
  // Sub-agent results
  subAgentResults?: DocumentationSubAgentResult[];
  documentationArtifacts?: {
    charts?: any[];
    dashboards?: any[];
    enhancedSections?: string[];
  };
  // Agent-specific metadata
  agentMetadata: {
    agentId: string;
    executionId: string;
    startTime: string;
    endTime?: string;
    totalExecutionTimeMs?: number;
    toolExecutionTimeMs?: number;
    agentOverheadMs?: number;
    subAgentExecutionTimeMs?: number;
    usedSubAgents?: boolean;
  };
}

/**
 * Helper function to get filenames from namespace IDs
 */
async function getFilenamesFromNamespaces(userId: string, namespaces: string[]): Promise<string[]> {
  if (!namespaces || namespaces.length === 0) {
    return [];
  }

  const filenames: string[] = [];

  try {
    // Query files collection for each namespace
    for (const namespace of namespaces) {
      try {
        const fileSnapshot = await adminDb.collection('users')
          .doc(userId)
          .collection('files')
          .where('namespace', '==', namespace)
          .limit(1)
          .get();

        if (!fileSnapshot.empty) {
          const fileData = fileSnapshot.docs[0].data();
          const filename = fileData.name || `Document-${namespace.substring(0, 8)}`;
          filenames.push(filename);
        }
      } catch (error) {
        console.warn(`Failed to get filename for namespace ${namespace}:`, error);
        // Add a fallback name if we can't get the actual filename
        filenames.push(`Document-${namespace.substring(0, 8)}`);
      }
    }
  } catch (error) {
    console.error('Error getting filenames from namespaces:', error);
  }

  return filenames;
}

export class CreateDocumentationAgent {
  private options: CreateDocumentationAgentOptions;
  private agentId: string;
  private chartTool: ChartTool;
  private dashboardTool: DashboardTool;

  constructor(options: CreateDocumentationAgentOptions = {} as CreateDocumentationAgentOptions) {
    this.options = {
      // Set defaults
      strategy: options.strategy || 'standalone',
      topK: options.topK || 5,
      modelProvider: options.modelProvider || 'google',
      modelName: options.modelName || 'gemini-2.5-pro',
      filters: options.filters || {},
      abTest: options.abTest || false,
      generatePDF: options.generatePDF !== false, // Default to true
      // Preserve full content by default in the agent wrapper unless explicitly requested
      removeTechnicalMetadata: options.removeTechnicalMetadata ?? false,
      includeExplanation: options.includeExplanation ?? false,
      streamResponse: options.streamResponse ?? false,
      createPMORecord: options.createPMORecord ?? true, // Default to true for PMO tracking
      pmoRecordTitle: options.pmoRecordTitle,
      // Sub-agent options (MANDATORY)
      maxSubAgents: options.maxSubAgents || 3,
      subAgentComplexity: options.subAgentComplexity || 'moderate',
      ...options
    };

    this.agentId = options.agentId || `doc-agent-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    // Validate mandatory useSubAgents parameter
    if (typeof options.useSubAgents !== 'boolean') {
      throw new Error('CreateDocumentationAgent: useSubAgents parameter is mandatory and must be a boolean');
    }

    // Initialize tools for sub-agents (ALWAYS - tools must be properly configured)
    try {
      this.chartTool = new ChartTool();
      this.dashboardTool = new DashboardTool();

      // Validate tool configuration
      this._validateToolConfiguration();
    } catch (error: any) {
      throw new Error(`CreateDocumentationAgent: Failed to initialize tools - ${error.message}. Ensure all tools are properly configured.`);
    }
  }

  /**
   * Create documentation using the wrapped tool
   * @param options - Documentation creation options
   * @returns - Agent result with metadata
   */
  async createDocumentation(options: CreateDocumentationAgentOptions): Promise<CreateDocumentationAgentResult> {
    const executionId = `exec-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const startTime = new Date().toISOString();
    const agentStartMs = Date.now();

    try {
      // Validate required parameters
      if (!options.userId || !options.category || !options.query) {
        throw new Error('Missing required parameters: userId, category, and query are required');
      }

      console.log(`CreateDocumentationAgent [${this.agentId}]: Starting documentation creation for query: "${options.query.substring(0, 100)}${options.query.length > 100 ? '...' : ''}"`);

      // Stream update: initializing
      if (this.options.streamResponse && this.options.onStreamUpdate) {
        this.options.onStreamUpdate({
          stage: 'initializing',
          message: 'Initializing documentation creation agent...',
          progress: 0
        });
      }

      // Merge options with agent defaults (keeping agent-specific flags)
      const agentOptions: CreateDocumentationAgentOptions = {
        ...this.options,
        ...options
      } as CreateDocumentationAgentOptions;

      // Build tool options by forwarding every tool parameter explicitly
      const toolOptions: CreateDocumentationToolOptions = {
        userId: agentOptions.userId,
        category: agentOptions.category,
        query: agentOptions.query,
        namespaces: agentOptions.namespaces,
        strategy: agentOptions.strategy,
        topK: agentOptions.topK,
        modelProvider: agentOptions.modelProvider,
        modelName: agentOptions.modelName,
        filters: agentOptions.filters,
        abTest: agentOptions.abTest,
        abStrategies: agentOptions.abStrategies,
        generatePDF: agentOptions.generatePDF,
        customTitle: agentOptions.customTitle,
        removeTechnicalMetadata: agentOptions.removeTechnicalMetadata
      };

      console.log(`CreateDocumentationAgent [${this.agentId}]: Using configuration:`, {
        strategy: toolOptions.strategy,
        modelProvider: toolOptions.modelProvider,
        modelName: toolOptions.modelName,
        generatePDF: toolOptions.generatePDF,
        abTest: toolOptions.abTest
      });

      // Stream update: querying codebase
      if (this.options.streamResponse && this.options.onStreamUpdate) {
        this.options.onStreamUpdate({
          stage: 'querying-codebase',
          message: 'Querying codebase for relevant information...',
          progress: 25
        });
      }

      // Call the underlying tool
      const toolStartMs = Date.now();
      console.log(`CreateDocumentationAgent [${this.agentId}]: Calling createDocumentationTool...`);

      const toolResult = await createDocumentationTool(toolOptions);

      const toolEndMs = Date.now();
      const toolExecutionTimeMs = toolEndMs - toolStartMs;

      console.log(`CreateDocumentationAgent [${this.agentId}]: Tool execution completed in ${toolExecutionTimeMs}ms`);

      if (!toolResult.success) {
        throw new Error(`Documentation tool failed: ${toolResult.error}`);
      }

      // Sub-agent processing (MANDATORY when useSubAgents is true)
      let subAgentResults: DocumentationSubAgentResult[] = [];
      let documentationArtifacts: any = {};
      let subAgentExecutionTimeMs = 0;

      if (agentOptions.useSubAgents) {
        if (!toolResult.markdownContent) {
          throw new Error('Cannot use sub-agents: base documentation content is empty');
        }
        const subAgentStartMs = Date.now();

        // Stream update: creating sub-agents
        if (this.options.streamResponse && this.options.onStreamUpdate) {
          this.options.onStreamUpdate({
            stage: 'creating-sub-agents',
            message: 'Creating specialized sub-agents for enhanced documentation...',
            progress: 50
          });
        }

        try {
          const subAgents = await this._createDocumentationSubAgents(
            agentOptions.query,
            agentOptions.category,
            toolResult.markdownContent,
            agentOptions.maxSubAgents || 3
          );

          // Stream update: executing sub-agents
          if (this.options.streamResponse && this.options.onStreamUpdate) {
            this.options.onStreamUpdate({
              stage: 'executing-sub-agents',
              message: 'Executing sub-agents for enhanced analysis...',
              progress: 60,
              subAgentProgress: {
                completed: 0,
                total: subAgents.length
              }
            });
          }

          subAgentResults = await this._executeDocumentationSubAgents(subAgents, toolResult.markdownContent);

          // Stream update: consolidating results
          if (this.options.streamResponse && this.options.onStreamUpdate) {
            this.options.onStreamUpdate({
              stage: 'consolidating-results',
              message: 'Consolidating sub-agent results...',
              progress: 70
            });
          }

          documentationArtifacts = await this._consolidateSubAgentResults(subAgentResults, toolResult.markdownContent);

          subAgentExecutionTimeMs = Date.now() - subAgentStartMs;
          console.log(`CreateDocumentationAgent [${this.agentId}]: Sub-agent execution completed in ${subAgentExecutionTimeMs}ms`);
        } catch (subAgentError: any) {
          console.warn(`CreateDocumentationAgent [${this.agentId}]: Sub-agent processing failed:`, subAgentError);
          // Continue without sub-agents
        }
      }

      // Stream update: generating content
      if (this.options.streamResponse && this.options.onStreamUpdate) {
        this.options.onStreamUpdate({
          stage: 'generating-content',
          message: 'Processing and formatting documentation content...',
          progress: 75
        });
      }

      // Stream update: creating PDF (if applicable)
      if (toolOptions.generatePDF && this.options.streamResponse && this.options.onStreamUpdate) {
        this.options.onStreamUpdate({
          stage: 'creating-pdf',
          message: 'Generating PDF document...',
          progress: 90
        });
      }

      // PMO Record Integration
      let pmoRecordResult: { pmoRecordId: string; assignedTeams: string[] } | undefined;

      if (agentOptions.createPMORecord) {
        try {
          console.log(`CreateDocumentationAgent [${this.agentId}]: Creating PMO record for documentation tracking...`);

          // Get filenames from namespaces for contextFiles
          const namespacesQueried = toolResult.metrics?.namespacesQueried || [];
          const contextFiles = await getFilenamesFromNamespaces(options.userId, namespacesQueried);

          // Create PMO record
          const pmoTitle = agentOptions.pmoRecordTitle || toolResult.generatedTitle || `Documentation: ${options.query.substring(0, 50)}${options.query.length > 50 ? '...' : ''}`;

          const pmoRecordId = await createPMORecordFromForm(options.userId, {
            title: pmoTitle,
            description: `Documentation generation request: ${options.query}`,
            priority: 'Medium',
            category: agentOptions.category, // Use the actual category from the request
            sourceFile: `Generated by CreateDocumentationAgent`,
            fileName: `${pmoTitle} - Documentation`,
            customContext: `Agent ID: ${this.agentId}\nStrategy: ${toolOptions.strategy}\nModel: ${toolOptions.modelProvider}/${toolOptions.modelName}\nNamespaces: ${namespacesQueried.join(', ')}`,
            pmoAssessment: `Automated documentation generation completed successfully. Generated ${toolResult.markdownContent?.length || 0} characters of content.${toolResult.pdfResult ? ' PDF document created.' : ''}\n\nFiles processed: ${contextFiles.join(', ')}`,
            selectedCategory: agentOptions.category, // Set contextCategories to match category
          });

          // Assign DocumentationGeneration team and update contextFiles
          const assignedTeams = [AgenticTeamId.DocumentationGeneration];

          await updatePMORecord(options.userId, pmoRecordId, {
            agentIds: assignedTeams,
            teamSelectionRationale: "DocumentationGeneration team automatically assigned for all documentation generation activities. This team specializes in creating comprehensive documentation using AI-powered tools and agents.",
            status: 'Completed',
            contextFiles: contextFiles.length > 0 ? contextFiles : null, // Set the actual filenames
            contextCategories: [agentOptions.category] // Ensure contextCategories matches the category
          });

          pmoRecordResult = {
            pmoRecordId,
            assignedTeams: assignedTeams.map(team => team.toString())
          };

          console.log(`CreateDocumentationAgent [${this.agentId}]: PMO record created successfully with ID: ${pmoRecordId}`);
        } catch (pmoError: any) {
          console.warn(`CreateDocumentationAgent [${this.agentId}]: Failed to create PMO record:`, pmoError);
          // Continue without failing the entire operation
        }
      }

      const endTime = new Date().toISOString();
      const agentEndMs = Date.now();
      const totalExecutionTimeMs = agentEndMs - agentStartMs;
      const agentOverheadMs = totalExecutionTimeMs - toolExecutionTimeMs;

      console.log(`CreateDocumentationAgent [${this.agentId}]: Documentation creation completed successfully`);
      console.log(`CreateDocumentationAgent [${this.agentId}]: Execution metrics:`, {
        totalExecutionTimeMs,
        toolExecutionTimeMs,
        agentOverheadMs,
        generatedTitle: toolResult.generatedTitle,
        contentLength: toolResult.markdownContent?.length || 0,
        pdfGenerated: !!toolResult.pdfResult,
        pmoRecordCreated: !!pmoRecordResult
      });

      // Stream update: complete
      if (this.options.streamResponse && this.options.onStreamUpdate) {
        this.options.onStreamUpdate({
          stage: 'complete',
          message: 'Documentation creation completed successfully!',
          progress: 100,
          data: {
            title: toolResult.generatedTitle,
            contentLength: toolResult.markdownContent?.length || 0,
            pdfGenerated: !!toolResult.pdfResult,
            pmoRecordCreated: !!pmoRecordResult
          }
        });
      }

      // Return agent result with metadata
      const agentResult: CreateDocumentationAgentResult = {
        ...toolResult, // Include all tool result properties
        pmoRecord: pmoRecordResult, // Include PMO record information
        subAgentResults: subAgentResults.length > 0 ? subAgentResults : undefined,
        documentationArtifacts: Object.keys(documentationArtifacts).length > 0 ? documentationArtifacts : undefined,
        agentMetadata: {
          agentId: this.agentId,
          executionId,
          startTime,
          endTime,
          totalExecutionTimeMs,
          toolExecutionTimeMs,
          agentOverheadMs,
          subAgentExecutionTimeMs,
          usedSubAgents: agentOptions.useSubAgents && subAgentResults.length > 0
        }
      };

      return agentResult;

    } catch (error: any) {
      const endTime = new Date().toISOString();
      const agentEndMs = Date.now();
      const totalExecutionTimeMs = agentEndMs - agentStartMs;

      console.error(`CreateDocumentationAgent [${this.agentId}] error:`, error);

      // Stream update: error
      if (this.options.streamResponse && this.options.onStreamUpdate) {
        this.options.onStreamUpdate({
          stage: 'complete',
          message: `Error: ${error.message}`,
          progress: 100
        });
      }

      return {
        success: false,
        query: options.query || '',
        category: options.category || '',
        strategy: options.strategy || 'standalone',
        markdownContent: '',
        rawData: {},
        error: error.message || "Unknown error occurred in CreateDocumentationAgent",
        agentMetadata: {
          agentId: this.agentId,
          executionId,
          startTime,
          endTime,
          totalExecutionTimeMs,
          toolExecutionTimeMs: 0,
          agentOverheadMs: totalExecutionTimeMs
        }
      };
    }
  }

  /**
   * Create specialized sub-agents for enhanced documentation analysis
   */
  private async _createDocumentationSubAgents(
    query: string,
    category: string,
    baseContent: string,
    maxSubAgents: number
  ): Promise<DocumentationSubAgent[]> {
    const prompt = `
You are a Documentation Strategy AI. Based on the user's query and the base documentation content,
create specialized sub-agents to enhance and expand the documentation with additional insights.

USER QUERY: "${query}"
CATEGORY: "${category}"
BASE CONTENT LENGTH: ${baseContent.length} characters

Create ${maxSubAgents} specialized sub-agents that would add the most value to this documentation.
Each sub-agent should have a specific focus area and use appropriate tools.

Available specializations:
- Technical Analysis: Deep dive into technical implementation details
- Architecture Review: System architecture and design patterns analysis
- API Documentation: Endpoint analysis and usage examples
- Performance Analysis: Performance considerations and optimizations
- Security Analysis: Security implications and best practices
- User Guide Creation: User-focused documentation and examples
- Integration Guide: Integration patterns and external dependencies
- Troubleshooting Guide: Common issues and solutions

For each sub-agent, determine:
1. Specialization area
2. Specific assignment related to the query
3. Priority level (high/medium/low)
4. Complexity level (simple/moderate/complex)
5. Which tools would be most useful

IMPORTANT: Respond with a valid JSON array only. Each sub-agent object must have these fields:
- specialization (string, required)
- assignment (string, required, minimum 10 characters)
- priority (string, optional: "high", "medium", or "low")
- complexity (string, optional: "simple", "moderate", or "complex")
- agentName (string, optional)
- tools (array of strings, optional)
- rationale (string, optional)

Example format:
[
  {
    "specialization": "Technical Analysis",
    "assignment": "Analyze the technical implementation details...",
    "priority": "high",
    "complexity": "complex"
  }
]

Respond with only the JSON array, no additional text or markdown formatting.`;

    try {
      const response = await processWithAnthropic({
        prompt: prompt,
        model: 'claude-sonnet-4-20250514',
        max_tokens: 2000,
        temperature: 0.3
      });

      // Parse and validate the response using Zod schema
      const subAgentsData = parseAndValidateSubAgents(response);

      console.log(`CreateDocumentationAgent [${this.agentId}]: Successfully created ${subAgentsData.length} sub-agents`);

      return subAgentsData.map((data, index: number) => ({
        agentId: `sub-agent-${Date.now()}-${index}`,
        agentName: data.agentName || `${data.specialization} Specialist`,
        specialization: data.specialization,
        assignment: data.assignment,
        priority: data.priority || 'medium',
        estimatedComplexity: data.estimatedComplexity || data.complexity || this.options.subAgentComplexity || 'moderate',
        enabledTools: this._determineToolsForSpecialization(data.specialization, data.estimatedComplexity || data.complexity || 'moderate'),
        toolUseGuidance: this._generateToolGuidance(data.specialization)
      }));
    } catch (error) {
      console.error(`CreateDocumentationAgent [${this.agentId}]: Failed to create dynamic sub-agents:`, error);

      // Log the specific error type for debugging
      if (error instanceof Error) {
        console.error(`CreateDocumentationAgent [${this.agentId}]: Error details:`, {
          message: error.message,
          name: error.name,
          stack: error.stack?.split('\n').slice(0, 3).join('\n') // First 3 lines of stack
        });
      }

      console.log(`CreateDocumentationAgent [${this.agentId}]: Falling back to predefined sub-agents`);
      return this._createFallbackSubAgents(query, category, maxSubAgents);
    }
  }

  /**
   * Create fallback sub-agents when dynamic creation fails
   */
  private _createFallbackSubAgents(query: string, category: string, maxSubAgents: number): DocumentationSubAgent[] {
    const fallbackAgents = [
      {
        specialization: 'Technical Analysis',
        assignment: `Provide detailed technical analysis of the implementation details related to: ${query}`,
        priority: 'high' as const,
        estimatedComplexity: 'moderate' as const
      },
      {
        specialization: 'Architecture Review',
        assignment: `Analyze the system architecture and design patterns relevant to: ${query}`,
        priority: 'medium' as const,
        estimatedComplexity: 'complex' as const
      },
      {
        specialization: 'User Guide Creation',
        assignment: `Create user-focused documentation and practical examples for: ${query}`,
        priority: 'medium' as const,
        estimatedComplexity: 'simple' as const
      }
    ];

    return fallbackAgents.slice(0, maxSubAgents).map((agent, index) => ({
      agentId: `fallback-agent-${Date.now()}-${index}`,
      agentName: `${agent.specialization} Specialist`,
      specialization: agent.specialization,
      assignment: agent.assignment,
      priority: agent.priority,
      estimatedComplexity: agent.estimatedComplexity,
      enabledTools: this._determineToolsForSpecialization(agent.specialization, agent.estimatedComplexity),
      toolUseGuidance: this._generateToolGuidance(agent.specialization)
    }));
  }

  /**
   * Determine which tools should be enabled for a specialization
   */
  private _determineToolsForSpecialization(specialization: string, complexity: string) {
    const baseTools = {
      chartGeneration: false,
      dashboardCreation: false,
      webSearch: false,
      datetime: false,
      questionAnswer: false
    };

    const toolMappings: Record<string, Partial<typeof baseTools>> = {
      'Technical Analysis': {
        chartGeneration: true,
        webSearch: true,
        datetime: true,
        questionAnswer: complexity !== 'simple'
      },
      'Architecture Review': {
        chartGeneration: true,
        dashboardCreation: complexity === 'complex',
        webSearch: true,
        datetime: true,
        questionAnswer: true
      },
      'API Documentation': {
        chartGeneration: true,
        webSearch: true,
        datetime: true,
        questionAnswer: true
      },
      'Performance Analysis': {
        chartGeneration: true,
        dashboardCreation: true,
        webSearch: true,
        datetime: true,
        questionAnswer: true
      },
      'Security Analysis': {
        chartGeneration: true,
        webSearch: true,
        datetime: true,
        questionAnswer: true
      },
      'User Guide Creation': {
        chartGeneration: complexity !== 'simple',
        webSearch: true,
        datetime: true,
        questionAnswer: true
      }
    };

    const specializationTools = toolMappings[specialization] || {
      chartGeneration: complexity !== 'simple',
      webSearch: true,
      datetime: true,
      questionAnswer: complexity !== 'simple'
    };

    return { ...baseTools, ...specializationTools };
  }

  /**
   * Generate tool usage guidance for a specialization
   */
  private _generateToolGuidance(specialization: string): string {
    const guidanceMap: Record<string, string> = {
      'Technical Analysis': 'Use charts to visualize technical concepts and data flows. Search web for current best practices and implementation patterns. Use datetime for analysis timestamps. Ask questions to clarify technical requirements.',
      'Architecture Review': 'Create architecture diagrams and system flow charts. Search for current architectural patterns and design principles. Use datetime for review timestamps. Ask questions about system boundaries and requirements.',
      'API Documentation': 'Generate API flow charts and endpoint visualizations. Search for API documentation standards and best practices. Use datetime for documentation timestamps. Ask questions about API usage patterns.',
      'Performance Analysis': 'Create performance charts and metrics dashboards. Search for current performance optimization techniques. Use datetime for analysis timestamps. Ask questions about performance requirements.',
      'Security Analysis': 'Generate security assessment charts and vulnerability visualizations. Search for current security threats and best practices. Use datetime for security assessment timestamps. Ask questions about security requirements.',
      'User Guide Creation': 'Use charts when helpful for user understanding. Search for current documentation standards and user experience best practices. Use datetime for guide timestamps. Ask questions about user needs and workflows.'
    };

    return guidanceMap[specialization] || 'Use available tools to enhance documentation quality. Search web for current best practices. Use datetime for timestamps. Ask questions when clarification is needed.';
  }

  /**
   * Execute documentation sub-agents
   */
  private async _executeDocumentationSubAgents(
    subAgents: DocumentationSubAgent[],
    baseContent: string
  ): Promise<DocumentationSubAgentResult[]> {
    const results: DocumentationSubAgentResult[] = [];

    for (let i = 0; i < subAgents.length; i++) {
      const subAgent = subAgents[i];

      // Stream update for current sub-agent
      if (this.options.streamResponse && this.options.onStreamUpdate) {
        this.options.onStreamUpdate({
          stage: 'executing-sub-agents',
          message: `Executing ${subAgent.agentName}...`,
          progress: 60 + (i / subAgents.length) * 10,
          subAgentProgress: {
            completed: i,
            total: subAgents.length,
            currentAgent: subAgent.agentName
          }
        });
      }

      const startTime = Date.now();

      try {
        const result = await this._executeSubAgent(subAgent, baseContent);
        results.push({
          ...result,
          executionTimeMs: Date.now() - startTime
        });
      } catch (error: any) {
        console.error(`Sub-agent ${subAgent.agentName} failed:`, error);
        results.push({
          agentId: subAgent.agentId,
          agentName: subAgent.agentName,
          specialization: subAgent.specialization,
          assignment: subAgent.assignment,
          output: '',
          success: false,
          error: error.message,
          executionTimeMs: Date.now() - startTime
        });
      }
    }

    return results;
  }

  /**
   * Execute a single sub-agent with agentic tool access
   */
  private async _executeSubAgent(
    subAgent: DocumentationSubAgent,
    baseContent: string
  ): Promise<DocumentationSubAgentResult> {
    const prompt = `
You are a ${subAgent.specialization} specialist. Your task is to enhance the provided documentation with your expertise.

ASSIGNMENT: ${subAgent.assignment}

BASE DOCUMENTATION:
${baseContent.substring(0, 4000)}${baseContent.length > 4000 ? '\n... (content truncated)' : ''}

TOOL GUIDANCE: ${subAgent.toolUseGuidance}

Please provide enhanced analysis and documentation for your specialization area. Use available tools when they would add value to your analysis.

Focus on:
1. Adding insights not covered in the base documentation
2. Providing specialized perspective from your expertise area
3. Using tools to create visualizations or gather additional information when helpful
4. Ensuring your output complements rather than duplicates the base content

Provide your enhanced documentation in markdown format.`;

    // Check if sub-agent has tool access
    if (subAgent.enabledTools && this._hasAnyToolEnabled(subAgent.enabledTools)) {
      return await this._executeSubAgentWithTools(subAgent, prompt);
    } else {
      return await this._executeSubAgentDirectly(subAgent, prompt);
    }
  }

  /**
   * Execute sub-agent with agentic tool access
   */
  private async _executeSubAgentWithTools(
    subAgent: DocumentationSubAgent,
    prompt: string
  ): Promise<DocumentationSubAgentResult> {
    try {
      const tools = this._buildToolDefinitions(subAgent.enabledTools!);

      const response = await processWithAnthropic({
        prompt: prompt,
        model: 'claude-sonnet-4-20250514',
        max_tokens: 4000,
        temperature: 0.3,
        modelOptions: {
          tools: tools.length > 0 ? tools : undefined
        }
      });

      // Parse response - processWithAnthropic always returns a string
      let content = response;
      let artifacts: any = {};

      // Note: Tool calls are not currently supported by processWithAnthropic
      // The function only returns text content, not structured responses with tool calls
      // If tool support is needed, a different implementation would be required

      return {
        agentId: subAgent.agentId,
        agentName: subAgent.agentName,
        specialization: subAgent.specialization,
        assignment: subAgent.assignment,
        output: content,
        success: true,
        artifacts: Object.keys(artifacts).length > 0 ? artifacts : undefined
      };
    } catch (error: any) {
      console.warn(`Agentic execution failed for ${subAgent.agentName}, falling back to direct execution:`, error);
      return await this._executeSubAgentDirectly(subAgent, prompt);
    }
  }

  /**
   * Execute sub-agent directly without tools
   */
  private async _executeSubAgentDirectly(
    subAgent: DocumentationSubAgent,
    prompt: string
  ): Promise<DocumentationSubAgentResult> {
    const response = await processWithAnthropic({
      prompt: prompt,
      model: 'claude-sonnet-4-20250514',
      max_tokens: 4000,
      temperature: 0.3
    });

    return {
      agentId: subAgent.agentId,
      agentName: subAgent.agentName,
      specialization: subAgent.specialization,
      assignment: subAgent.assignment,
      output: typeof response === 'string' ? response : String(response),
      success: true
    };
  }

  /**
   * Check if any tools are enabled
   */
  private _hasAnyToolEnabled(enabledTools: NonNullable<DocumentationSubAgent['enabledTools']>): boolean {
    return !!(enabledTools.chartGeneration || enabledTools.dashboardCreation ||
              enabledTools.webSearch || enabledTools.datetime || enabledTools.questionAnswer);
  }

  /**
   * Build function definitions for enabled tools
   */
  private _buildToolDefinitions(enabledTools: NonNullable<DocumentationSubAgent['enabledTools']>): any[] {
    const tools: any[] = [];

    if (enabledTools.chartGeneration) {
      tools.push({
        type: "function",
        function: {
          name: "generate_chart",
          description: "Generate a chart visualization to enhance documentation understanding",
          parameters: {
            type: "object",
            properties: {
              prompt: {
                type: "string",
                description: "Detailed description of the chart to generate"
              },
              chartType: {
                type: "string",
                enum: ["bar", "line", "pie", "scatter", "flow", "heatmap", "table", "bubble"],
                description: "Type of chart most appropriate for the data"
              }
            },
            required: ["prompt", "chartType"]
          }
        }
      });
    }

    if (enabledTools.dashboardCreation) {
      tools.push({
        type: "function",
        function: {
          name: "create_dashboard",
          description: "Create a multi-chart dashboard for comprehensive visualization",
          parameters: {
            type: "object",
            properties: {
              title: { type: "string", description: "Dashboard title" },
              description: { type: "string", description: "Dashboard description" },
              charts: {
                type: "array",
                items: {
                  type: "object",
                  properties: {
                    prompt: { type: "string" },
                    chartType: { type: "string" }
                  }
                }
              }
            },
            required: ["title", "description", "charts"]
          }
        }
      });
    }

    if (enabledTools.webSearch) {
      tools.push({
        type: "function",
        function: {
          name: "web_search",
          description: "Search the internet for current information and best practices",
          parameters: {
            type: "object",
            properties: {
              query: { type: "string", description: "Search query" },
              numResults: { type: "number", description: "Number of results (default: 5)" }
            },
            required: ["query"]
          }
        }
      });
    }

    if (enabledTools.datetime) {
      tools.push({
        type: "function",
        function: {
          name: "get_datetime",
          description: "Get current date and time for documentation timestamps",
          parameters: {
            type: "object",
            properties: {
              format: { type: "string", enum: ["full", "long", "medium", "short"] },
              includeTime: { type: "boolean" }
            }
          }
        }
      });
    }

    if (enabledTools.questionAnswer) {
      tools.push({
        type: "function",
        function: {
          name: "ask_question",
          description: "Ask questions to gather additional context or clarify requirements",
          parameters: {
            type: "object",
            properties: {
              query: { type: "string", description: "Question to ask" },
              context: { type: "string", description: "Additional context" }
            },
            required: ["query"]
          }
        }
      });
    }

    return tools;
  }

  /**
   * Process tool calls from sub-agent responses
   * NOTE: Currently unused as processWithAnthropic doesn't support tool calls
   * Kept for future implementation when tool support is added
   */
  private async _processToolCalls(_toolCalls: any[], _subAgent: DocumentationSubAgent): Promise<{ artifacts: any; additionalContent: string }> {
    const artifacts: any = {};
    let additionalContent = '';

    for (const toolCall of _toolCalls) {
      try {
        const result = await this._executeToolCall(toolCall, _subAgent);

        if (result.success) {
          if (toolCall.name === 'generate_chart') {
            artifacts.charts = artifacts.charts || [];
            artifacts.charts.push(result.result);
          } else if (toolCall.name === 'create_dashboard') {
            artifacts.dashboards = artifacts.dashboards || [];
            artifacts.dashboards.push(result.result);
          } else if (toolCall.name === 'web_search') {
            additionalContent += `\n\n**Web Search Results:**\n${result.result}\n`;
          } else if (toolCall.name === 'get_datetime') {
            additionalContent += `\n\n**Timestamp:** ${result.result}\n`;
          } else if (toolCall.name === 'ask_question') {
            additionalContent += `\n\n**Question Analysis:**\n${result.result}\n`;
          }
        }
      } catch (error) {
        console.warn(`Tool call ${toolCall.name} failed:`, error);
      }
    }

    return { artifacts, additionalContent };
  }

  /**
   * Execute a specific tool call
   */
  private async _executeToolCall(toolCall: any, _subAgent: DocumentationSubAgent): Promise<{ success: boolean; result?: any; error?: string }> {
    try {
      const { name, input } = toolCall;

      switch (name) {
        case 'generate_chart':
          const chartResult = await this.chartTool.generateChart({
            prompt: input.prompt,
            chartType: input.chartType
          });
          return { success: chartResult.success, result: chartResult };

        case 'create_dashboard':
          const dashboardResult = await this.dashboardTool.generateDashboard({
            prompt: `${input.title}. ${input.description}`,
            layout: 'grid'
          });
          return { success: dashboardResult.success, result: dashboardResult };

        case 'web_search':
          const searchResult = await internetSearchTool.search(input.query, {
            numResults: input.numResults || 5
          });
          return {
            success: searchResult.success,
            result: searchResult.formattedResults,
            error: searchResult.success ? undefined : searchResult.metadata.error
          };

        case 'get_datetime':
          const datetimeResult = dateTimeTool.getCurrentDateTime({
            format: input.format || 'medium',
            includeTime: input.includeTime !== false
          });
          return { success: true, result: datetimeResult };

        case 'ask_question':
          const questionResult = await questionAnswerTool.process({
            query: input.query,
            context: input.context,
            userId: this.options.userId!,
            maxQuestions: 2
          });
          return {
            success: questionResult.success,
            result: questionResult.finalAnswer,
            error: questionResult.success ? undefined : questionResult.error
          };

        default:
          return { success: false, error: `Unknown tool: ${name}` };
      }
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  }

  /**
   * Consolidate sub-agent results into enhanced documentation
   */
  private async _consolidateSubAgentResults(
    subAgentResults: DocumentationSubAgentResult[],
    baseContent: string
  ): Promise<any> {
    const artifacts: any = {
      charts: [],
      dashboards: [],
      enhancedSections: []
    };

    // Collect all artifacts from sub-agents
    subAgentResults.forEach(result => {
      if (result.artifacts?.charts) {
        artifacts.charts.push(...result.artifacts.charts);
      }
      if (result.artifacts?.dashboards) {
        artifacts.dashboards.push(...result.artifacts.dashboards);
      }
      if (result.success && result.output) {
        artifacts.enhancedSections.push({
          specialization: result.specialization,
          agentName: result.agentName,
          content: result.output
        });
      }
    });

    return artifacts;
  }

  /**
   * Validate that all required tools are properly configured
   */
  private _validateToolConfiguration(): void {
    // Validate ChartTool
    if (!this.chartTool || typeof this.chartTool.generateChart !== 'function') {
      throw new Error('ChartTool is not properly configured');
    }

    // Validate DashboardTool
    if (!this.dashboardTool || typeof this.dashboardTool.generateDashboard !== 'function') {
      throw new Error('DashboardTool is not properly configured');
    }

    // Validate internetSearchTool
    if (!internetSearchTool || typeof internetSearchTool.search !== 'function') {
      throw new Error('internetSearchTool is not properly configured');
    }

    // Validate dateTimeTool
    if (!dateTimeTool || typeof dateTimeTool.getCurrentDateTime !== 'function') {
      throw new Error('dateTimeTool is not properly configured');
    }

    // Validate questionAnswerTool
    if (!questionAnswerTool || typeof questionAnswerTool.process !== 'function') {
      throw new Error('questionAnswerTool is not properly configured');
    }

    // Validate queryDocumentsTool
    if (!queryDocumentsTool) {
      throw new Error('queryDocumentsTool is not properly configured');
    }

    console.log(`CreateDocumentationAgent [${this.agentId}]: All tools validated successfully`);
  }
}

// Class is already exported above with 'export class CreateDocumentationAgent'
// No singleton instance due to mandatory parameters
// Users must create instances with required useSubAgents parameter
// Example: const agent = new CreateDocumentationAgent({ useSubAgents: true });
