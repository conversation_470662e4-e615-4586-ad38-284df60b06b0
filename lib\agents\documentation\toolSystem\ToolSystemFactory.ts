/**
 * Tool System Factory
 * 
 * Separate factory to avoid circular import issues
 */

import { LLMProvider, ToolSystemConfig, ToolSystemAdapter } from './ToolSystemAdapter';

/**
 * Factory function to create appropriate tool system adapter
 * Uses dynamic imports to avoid circular dependencies and hoisting issues
 */
export async function createToolSystemAdapter(config: ToolSystemConfig): Promise<ToolSystemAdapter> {
  switch (config.provider) {
    case 'anthropic': {
      const { AnthropicToolAdapter } = await import('./adapters/AnthropicToolAdapter');
      return new AnthropicToolAdapter(config);
    }
    case 'openai': {
      const { OpenAIToolAdapter } = await import('./adapters/OpenAIToolAdapter');
      return new OpenAIToolAdapter(config);
    }
    case 'google': {
      const { GoogleToolAdapter } = await import('./adapters/GoogleToolAdapter');
      return new GoogleToolAdapter(config);
    }
    case 'groq': {
      const { GroqToolAdapter } = await import('./adapters/GroqToolAdapter');
      return new GroqToolAdapter(config);
    }
    case 'deepseek': {
      const { DeepSeekToolAdapter } = await import('./adapters/DeepSeekToolAdapter');
      return new DeepSeekToolAdapter(config);
    }
    default:
      throw new Error(`Unsupported LLM provider: ${config.provider}`);
  }
}

/**
 * Synchronous factory for cases where dynamic imports aren't suitable
 * Only supports Anthropic for now to avoid import issues
 */
export function createToolSystemAdapterSync(config: ToolSystemConfig): ToolSystemAdapter {
  if (config.provider !== 'anthropic') {
    throw new Error(`Synchronous factory only supports Anthropic provider, got: ${config.provider}`);
  }
  
  // Import only Anthropic adapter to avoid circular dependencies
  const { AnthropicToolAdapter } = require('./adapters/AnthropicToolAdapter');
  return new AnthropicToolAdapter(config);
}
