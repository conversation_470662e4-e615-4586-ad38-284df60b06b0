/**
 * OpenAI-specific tool adapter
 * 
 * Handles OpenAI's function calling format and execution
 */

import {
  ToolSystemAdapter,
  BaseToolDefinition,
  BaseToolCall,
  OpenAIToolDefinition,
  OpenAIToolCall,
  ToolExecutionResult,
  ToolSystemConfig
} from '../ToolSystemAdapter';

export class OpenAIToolAdapter extends ToolSystemAdapter {
  constructor(config: ToolSystemConfig) {
    super(config);
  }

  /**
   * Convert base tool definitions to OpenAI's function calling format
   */
  formatToolsForProvider(tools: BaseToolDefinition[]): OpenAIToolDefinition[] {
    return tools.map(tool => ({
      type: 'function' as const,
      function: {
        name: tool.name,
        description: tool.description,
        parameters: {
          type: 'object' as const,
          properties: tool.parameters.properties,
          required: tool.parameters.required || []
        }
      }
    }));
  }

  /**
   * Parse tool calls from OpenAI response
   */
  parseToolCalls(response: any): BaseToolCall[] {
    const toolCalls: BaseToolCall[] = [];

    // Handle OpenAI response format
    if (response && response.choices && response.choices[0]) {
      const message = response.choices[0].message;
      
      if (message && message.tool_calls && Array.isArray(message.tool_calls)) {
        for (const toolCall of message.tool_calls) {
          if (toolCall.type === 'function' && toolCall.function) {
            try {
              const input = JSON.parse(toolCall.function.arguments);
              toolCalls.push({
                name: toolCall.function.name,
                input: input
              });
            } catch (error) {
              console.error('Failed to parse tool call arguments:', error);
            }
          }
        }
      }
    }

    return toolCalls;
  }

  /**
   * Execute a tool call (reuses the same implementation as Anthropic)
   */
  async executeToolCall(toolCall: BaseToolCall): Promise<ToolExecutionResult> {
    try {
      switch (toolCall.name) {
        case 'generate_chart':
          return await this.executeChartGeneration(toolCall.input);
        
        case 'create_dashboard':
          return await this.executeDashboardCreation(toolCall.input);
        
        case 'web_search':
          return await this.executeWebSearch(toolCall.input);
        
        case 'get_datetime':
          return await this.executeGetDateTime(toolCall.input);
        
        case 'ask_question':
          return await this.executeAskQuestion(toolCall.input);
        
        default:
          return {
            success: false,
            error: `Unknown tool: ${toolCall.name}`
          };
      }
    } catch (error: any) {
      return {
        success: false,
        error: `Tool execution failed: ${error.message}`
      };
    }
  }

  // Tool execution methods (same as Anthropic adapter)
  private async executeChartGeneration(input: any): Promise<ToolExecutionResult> {
    const { prompt, chartType } = input;
    
    if (!prompt || !chartType) {
      return {
        success: false,
        error: 'Missing required parameters: prompt and chartType'
      };
    }

    const chartData = {
      id: `chart-${Date.now()}`,
      type: chartType,
      prompt: prompt,
      data: {},
      config: {}
    };

    return {
      success: true,
      result: chartData,
      artifacts: {
        charts: [chartData]
      }
    };
  }

  private async executeDashboardCreation(input: any): Promise<ToolExecutionResult> {
    const { title, description, charts } = input;
    
    if (!title || !description || !charts) {
      return {
        success: false,
        error: 'Missing required parameters: title, description, and charts'
      };
    }

    const dashboardData = {
      id: `dashboard-${Date.now()}`,
      title,
      description,
      charts: charts.map((chart: any, index: number) => ({
        id: `chart-${Date.now()}-${index}`,
        type: chart.chartType,
        prompt: chart.prompt,
        data: {},
        config: {}
      }))
    };

    return {
      success: true,
      result: dashboardData,
      artifacts: {
        dashboards: [dashboardData],
        charts: dashboardData.charts
      }
    };
  }

  private async executeWebSearch(input: any): Promise<ToolExecutionResult> {
    const { query, numResults = 5 } = input;
    
    if (!query) {
      return {
        success: false,
        error: 'Missing required parameter: query'
      };
    }

    const searchResults = {
      query,
      results: Array.from({ length: Math.min(numResults, 5) }, (_, i) => ({
        title: `Search Result ${i + 1}`,
        url: `https://example.com/result-${i + 1}`,
        snippet: `This is a sample search result for "${query}"`
      }))
    };

    return {
      success: true,
      result: searchResults
    };
  }

  private async executeGetDateTime(input: any): Promise<ToolExecutionResult> {
    const { format = 'medium', includeTime = true } = input;
    
    const now = new Date();
    let formattedDate: string;

    switch (format) {
      case 'full':
        formattedDate = now.toLocaleDateString('en-US', { 
          weekday: 'long', 
          year: 'numeric', 
          month: 'long', 
          day: 'numeric' 
        });
        break;
      case 'long':
        formattedDate = now.toLocaleDateString('en-US', { 
          year: 'numeric', 
          month: 'long', 
          day: 'numeric' 
        });
        break;
      case 'short':
        formattedDate = now.toLocaleDateString('en-US');
        break;
      case 'medium':
      default:
        formattedDate = now.toLocaleDateString('en-US', { 
          year: 'numeric', 
          month: 'short', 
          day: 'numeric' 
        });
        break;
    }

    if (includeTime) {
      const timeString = now.toLocaleTimeString('en-US');
      formattedDate += ` ${timeString}`;
    }

    return {
      success: true,
      result: {
        timestamp: now.toISOString(),
        formatted: formattedDate,
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
      }
    };
  }

  private async executeAskQuestion(input: any): Promise<ToolExecutionResult> {
    const { query, context } = input;
    
    if (!query) {
      return {
        success: false,
        error: 'Missing required parameter: query'
      };
    }

    const response = {
      question: query,
      context: context || '',
      answer: `This is a placeholder response to the question: "${query}". In a real implementation, this would be processed by an appropriate Q&A system.`,
      confidence: 0.8,
      sources: []
    };

    return {
      success: true,
      result: response
    };
  }
}
