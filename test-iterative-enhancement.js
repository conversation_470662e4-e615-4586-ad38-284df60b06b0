/**
 * Test script for the new iterative enhancement system
 * This demonstrates the sophisticated iterative approach where the createDocumentationAgent
 * assesses sub-agent output quality and creates additional tasks if needed.
 */

const { CreateDocumentationAgent } = require('./lib/agents/documentation/createDocumentationAgent');

async function testIterativeEnhancement() {
  console.log('🚀 Testing Iterative Documentation Enhancement System');
  console.log('=' .repeat(60));

  try {
    // Create agent with iterative enhancement enabled
    const agent = new CreateDocumentationAgent({
      useSubAgents: true,
      maxSubAgents: 2, // Start with fewer agents to test iteration
      enableIterativeEnhancement: true,
      maxIterations: 2,
      qualityThreshold: 0.85,
      streamResponse: true,
      onStreamUpdate: (update) => {
        console.log(`📊 [${update.stage}] ${update.message}`);
        if (update.iteration) {
          console.log(`   🔄 Iteration ${update.iteration.current}/${update.iteration.total}`);
        }
        if (update.progress) {
          console.log(`   📈 Progress: ${update.progress}%`);
        }
      }
    });

    console.log('✅ Agent created with iterative enhancement enabled');
    console.log(`   - Max iterations: 2`);
    console.log(`   - Quality threshold: 0.85`);
    console.log(`   - Max sub-agents: 2`);
    console.log('');

    // Test with a simple documentation request
    const result = await agent.createDocumentation({
      userId: '<EMAIL>',
      category: 'Technical Documentation',
      query: 'Create comprehensive documentation for the PMO system including user guides and technical details',
      paths: ['components/PMO'],
      includeTests: false,
      includeComments: true,
      maxTokens: 4000
    });

    console.log('');
    console.log('🎉 Iterative Enhancement Test Results:');
    console.log('=' .repeat(60));
    console.log(`✅ Success: ${result.success}`);
    console.log(`📄 Content length: ${result.content?.length || 0} characters`);
    
    if (result.artifacts) {
      console.log(`📊 Enhanced sections: ${result.artifacts.enhancedSections?.length || 0}`);
      console.log(`📈 Charts generated: ${result.artifacts.charts?.length || 0}`);
      console.log(`📋 Dashboards created: ${result.artifacts.dashboards?.length || 0}`);
      
      if (result.artifacts.qualityMetrics) {
        console.log('');
        console.log('📊 Quality Metrics:');
        Object.entries(result.artifacts.qualityMetrics).forEach(([key, value]) => {
          console.log(`   ${key}: ${typeof value === 'number' ? value.toFixed(2) : value}`);
        });
      }
      
      if (result.artifacts.iterationHistory) {
        console.log('');
        console.log('🔄 Iteration History:');
        result.artifacts.iterationHistory.forEach((iteration, index) => {
          console.log(`   Iteration ${iteration.iteration}: Score ${iteration.qualityScore.toFixed(2)}, ${iteration.gaps.length} gaps identified`);
        });
      }
    }

    console.log('');
    console.log('🎯 Key Features Demonstrated:');
    console.log('   ✅ Quality assessment of sub-agent outputs');
    console.log('   ✅ Gap identification and analysis');
    console.log('   ✅ Dynamic generation of additional tasks');
    console.log('   ✅ Iterative improvement until quality threshold met');
    console.log('   ✅ Convergence detection and stopping criteria');
    console.log('   ✅ Progress tracking and metrics collection');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  testIterativeEnhancement()
    .then(() => {
      console.log('');
      console.log('🏁 Test completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Test failed with error:', error);
      process.exit(1);
    });
}

module.exports = { testIterativeEnhancement };
